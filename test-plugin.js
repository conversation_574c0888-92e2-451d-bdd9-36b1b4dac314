import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkDirective from 'remark-directive';
import remarkRehype from 'remark-rehype';
import rehypeStringify from 'rehype-stringify';
import { remarkGoogleMap } from './src/plugins/remark-google-map.js';

const processor = unified()
  .use(remarkParse)
  .use(remarkDirective)
  .use(remarkGoogleMap)
  .use(remarkRehype)
  .use(rehypeStringify);

const markdown = `
# 测试 GoogleMap 插件

这是一个简单的测试：

::googlemap{address="北京天安门"}

测试完成。
`;

async function test() {
  try {
    const result = await processor.process(markdown);
    console.log('转换成功！');
    console.log(result.toString());
  } catch (error) {
    console.error('转换失败：', error);
  }
}

test();
