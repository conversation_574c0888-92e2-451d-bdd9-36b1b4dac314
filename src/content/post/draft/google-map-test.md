---
title: "GoogleMap 插件测试"
description: "测试 GoogleMap 插件的各种功能和用法"
publishDate: "2025-11-15T10:00:00+08:00"
draft: true
tags: ["测试", "插件"]
---

# GoogleMap 插件测试

这篇文章用来测试新开发的 GoogleMap 插件功能。

## 基本用法

最简单的用法，只需要提供地址：

::googlemap{address="北京天安门"}

## 自定义尺寸

可以自定义地图的宽度和高度：

::googlemap{address="上海外滩" width="800" height="400"}

## 指定缩放级别

可以设置地图的缩放级别（1-20，数字越大越详细）：

::googlemap{address="广州塔" zoom="18"}

## 不同的地图类型

支持不同的地图类型：

### 卫星地图
::googlemap{address="杭州西湖" maptype="satellite"}

### 混合地图
::googlemap{address="深圳平安金融中心" maptype="hybrid"}

### 地形地图
::googlemap{address="黄山风景区" maptype="terrain"}

## 国外地址测试

测试国外地址的显示：

::googlemap{address="Eiffel Tower, Paris, France"}

::googlemap{address="Times Square, New York, USA" width="700" height="350"}

## 详细地址测试

测试更详细的地址格式：

::googlemap{address="中国北京市朝阳区国贸CBD"}

::googlemap{address="浙江省杭州市西湖区西湖风景名胜区"}

## 小尺寸地图

测试小尺寸地图的显示效果：

::googlemap{address="成都宽窄巷子" width="400" height="300"}

## 使用说明

GoogleMap 插件支持以下参数：

- `address` 或 `location`: 地址（必需）
- `width`: 地图宽度，默认 600px
- `height`: 地图高度，默认 450px  
- `zoom`: 缩放级别，默认 15
- `maptype`: 地图类型，支持 roadmap（默认）、satellite、hybrid、terrain

插件特点：
1. 异步加载，不会阻塞页面渲染
2. 加载失败时会显示错误信息
3. 响应式设计，在移动设备上自适应
4. 支持中英文地址

## 错误处理测试

测试无效地址的错误处理：

::googlemap{address="这是一个不存在的地址12345"}
