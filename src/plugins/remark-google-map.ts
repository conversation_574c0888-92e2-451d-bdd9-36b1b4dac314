import type { Root } from "mdast";
import type { Plugin } from "unified";
import { visit } from "unist-util-visit";
import { h, isNodeDirective } from "../utils/remark";

const DIRECTIVE_NAME = "googlemap";

export const remarkGoogleMap: Plugin<[], Root> = () => (tree) => {
	visit(tree, (node, index, parent) => {
		if (!parent || index === undefined || !isNodeDirective(node)) return;

		// We only want a leaf directive named DIRECTIVE_NAME
		if (node.type !== "leafDirective" || node.name !== DIRECTIVE_NAME) return;

		const address = node.attributes?.address || node.attributes?.location || null;
		if (!address) return; // Leave the directive as-is if no address is provided

		// Extract optional parameters
		const width = node.attributes?.width || "600";
		const height = node.attributes?.height || "450";
		const zoom = node.attributes?.zoom || "15";
		const maptype = node.attributes?.maptype || "roadmap"; // roadmap, satellite, hybrid, terrain

		const SimpleUUID = `GM-${crypto.randomUUID()}`;

		// Create the script that will handle the Google Maps embedding
		const script = h("script", {}, [
			{
				type: "text",
				value: `
				(function() {
					const mapContainer = document.getElementById('${SimpleUUID}');
					const address = "${address.replace(/"/g, '\\"')}";
					const width = "${width}";
					const height = "${height}";
					const zoom = "${zoom}";
					const maptype = "${maptype}";
					
					// Function to load map
					function loadMap() {
						try {
							mapContainer.classList.remove("gm-loading");
							
							// Create iframe element
							const iframe = document.createElement('iframe');
							iframe.width = width;
							iframe.height = height;
							iframe.style.border = '0';
							iframe.allowFullscreen = true;
							iframe.loading = 'lazy';
							iframe.referrerPolicy = 'no-referrer-when-downgrade';
							
							// Use Google Maps embed URL
							const embedUrl = 'https://www.google.com/maps?q=' + encodeURIComponent(address) + '&output=embed';
							iframe.src = embedUrl;
							
							// Handle iframe load error
							iframe.onerror = function() {
								mapContainer.classList.add("gm-error");
								mapContainer.innerHTML = '<div class="gm-error-message">无法加载地图: ' + address + '</div>';
							};
							
							// Replace loading content with iframe
							const mapContent = mapContainer.querySelector('.gm-content');
							if (mapContent) {
								mapContent.innerHTML = '';
								mapContent.appendChild(iframe);
							}
							
						} catch (err) {
							mapContainer.classList.add("gm-error");
							mapContainer.innerHTML = '<div class="gm-error-message">地图加载失败: ' + address + '</div>';
							console.warn("[GOOGLE-MAP] Error loading map for ${address} | ${SimpleUUID}.", err);
						}
					}
					
					// Load map with a small delay to avoid blocking page load
					setTimeout(loadMap, 100);
				})();
				`,
			},
		]);

		// Create the map container structure
		const mapTitle = h("div", { class: "gm-title" }, [
			h("span", { class: "gm-icon" }, [{ type: "text", value: "📍" }]),
			h("span", { class: "gm-address" }, [{ type: "text", value: address }]),
		]);

		const mapContent = h("div", { class: "gm-content" }, [
			h("div", { class: "gm-loading-text" }, [
				{ type: "text", value: "正在加载地图..." },
			]),
		]);

		// Replace the directive with the map container
		parent.children.splice(
			index,
			1,
			h("div", { id: SimpleUUID, class: "google-map gm-loading" }, [
				mapTitle,
				mapContent,
				script,
			]),
		);
	});
};
