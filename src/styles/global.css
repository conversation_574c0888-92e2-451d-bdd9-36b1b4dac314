/* would like to ignore ./src/pages/og-image/[slug].png.ts */
@import "tailwindcss";
/* config for tailwindcss-typography plugin */
@config "../../tailwind.config.ts";

/* use a selector-based strategy for dark mode */
@variant dark (&:where([data-theme="dark"], [data-theme="dark"] *));

/* 基于 #00769B 主题色调整的色彩配置 */
@theme {
	--color-global-bg: oklch(98.48% 0 0);
	--color-global-text: oklch(26.99% 0.0096 235.05);
	--color-link: oklch(45% 0.12 205); /* 基于 #00769B 调整的链接色 */
	--color-accent: oklch(45% 0.12 205); /* #00769B 对应的 OKLCH 值 */
	--color-accent-2: oklch(20% 0.02 205); /* 基于主题色的深色强调 */
	--color-quote: oklch(50% 0.1 205); /* 基于主题色调整的引用色 */
}

@layer base {
	html {
		color-scheme: light dark;
		accent-color: var(--color-accent);
		scrollbar-gutter: stable;
		font-family: "DM Sans Variable", "DM Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans SC", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
		font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
		text-rendering: optimizeLegibility;
		-webkit-font-smoothing: antialiased;
		-moz-osx-font-smoothing: grayscale;

		&[data-theme="light"] {
			color-scheme: light;
		}

		&[data-theme="dark"] {
			color-scheme: dark;
			--color-global-bg: oklch(23.64% 0.0045 248);
			--color-global-text: oklch(83.54% 0 264);
			--color-link: oklch(65% 0.15 205); /* 深色模式下基于主题色的链接色 */
			--color-accent: oklch(60% 0.15 205); /* 深色模式下的主题色 */
			--color-accent-2: oklch(90% 0.02 205); /* 深色模式下的浅色强调 */
			--color-quote: oklch(70% 0.12 205); /* 深色模式下的引用色 */
		}
	}

	:target {
		scroll-margin-block: 5ex;
	}

	/* 中文字体优化 */
	body {
		line-height: 1.7;
		font-weight: 400;
		letter-spacing: 0.01em;
	}

	/* 针对中文字符的特殊优化 */
	:lang(zh) {
		font-weight: 400;
		letter-spacing: 0.02em;
	}

	/* 标题字体优化 */
	h1, h2, h3, h4, h5, h6 {
		font-weight: 600;
		line-height: 1.4;
		letter-spacing: -0.01em;
	}

	/* 段落文本优化 */
	p {
		line-height: 1.8;
		font-weight: 400;
	}

	@view-transition {
		navigation: auto;
	}

	/* Astro image responsive styles, modified from -> https://docs.astro.build/en/guides/images/#responsive-image-styles */
	:where([data-astro-image]) {
		object-fit: var(--fit);
		object-position: var(--pos);
	}
	[data-astro-image="full-width"] {
		width: 100%;
	}
	[data-astro-image="constrained"] {
		max-width: 100%;
	}
}

@layer components {
	@import "./components/admonition.css";
	@import "./components/github-card.css";
	@import "./components/google-map.css";

	.cactus-link {
		@apply hover:decoration-link underline underline-offset-3 hover:decoration-2;
	}

	.title {
		@apply text-accent-2 text-2xl font-semibold;
	}

	.title-min {
		@apply text-accent-2 text-lg font-semibold;
	}

	/* 引用块样式 - 符合网站全局风格 */
	.prose blockquote {
		@apply bg-global-text/5 rounded-md py-1;
	}
}

@utility prose {
	--tw-prose-body: var(--color-global-text);
	--tw-prose-bold: var(--color-global-text);
	--tw-prose-bullets: var(--color-global-text);
	--tw-prose-code: var(--color-global-text);
	--tw-prose-headings: var(--color-accent-2);
	--tw-prose-hr: 0.5px dashed #666;
	--tw-prose-links: var(--color-global-text);
	--tw-prose-quotes: var(--color-global-text);
	--tw-prose-th-borders: #666;
}
