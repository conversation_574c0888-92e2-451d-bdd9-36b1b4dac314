.google-map {
	@apply bg-global-text/5 rounded-md px-4 py-3 mb-4;

	.gm-title {
		@apply relative flex items-center gap-2 text-base mb-3;

		.gm-icon {
			@apply text-lg flex-none;
		}
		
		.gm-address {
			@apply line-clamp-2 font-medium text-accent-2;
		}
	}

	.gm-content {
		@apply relative overflow-hidden rounded-md;
		
		iframe {
			@apply w-full h-auto border-0 rounded-md;
			min-height: 300px;
		}
		
		.gm-loading-text {
			@apply flex items-center justify-center h-64 text-global-text/60 bg-global-text/10 rounded-md;
		}
	}

	.gm-error-message {
		@apply flex items-center justify-center h-32 text-red-500 bg-red-50 dark:bg-red-900/20 rounded-md p-4 text-center;
	}

	&.gm-loading {
		.gm-title .gm-address {
			@apply bg-global-text/50 animate-pulse rounded-xl text-transparent;
		}
		
		.gm-content .gm-loading-text {
			@apply animate-pulse;
		}
	}

	&.gm-error {
		.gm-content {
			@apply bg-red-50 dark:bg-red-900/20;
		}
	}
}

/* 响应式设计 */
@media (max-width: 640px) {
	.google-map {
		@apply px-3 py-2;
		
		.gm-content iframe {
			min-height: 250px;
		}
	}
}
